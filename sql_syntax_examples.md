# Comprehensive SQL Syntax Examples

This guide provides practical SQL examples with sample data and expected outputs for common database operations.

## Sample Database Schema

We'll use these sample tables throughout the examples:

### employees

```sql
CREATE TABLE employees (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    department VARCHAR(50),
    salary DECIMAL(10,2),
    hire_date DATE
);
```

### departments

```sql
CREATE TABLE departments (
    id INT PRIMARY KEY,
    name VARCHAR(50),
    manager_id INT
);
```

### customers

```sql
CREATE TABLE customers (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100),
    city VARCHAR(50)
);
```

### orders

```sql
CREATE TABLE orders (
    id INT PRIMARY KEY,
    customer_id INT,
    order_date DATE,
    total_amount DECIMAL(10,2)
);
```

## Sample Data

**employees table:**
| id | name | department | salary | hire_date |
|----|------|------------|--------|-----------|
| 1 | <PERSON> | Engineering | 75000.00 | 2020-01-15 |
| 2 | <PERSON> | Marketing | 65000.00 | 2019-03-20 |
| 3 | Bob Johnson | Engineering | 80000.00 | 2021-06-10 |
| 4 | Alice Brown | HR | 55000.00 | 2018-11-05 |
| 5 | Charlie Wilson | Marketing | 70000.00 | 2020-09-12 |

**departments table:**
| id | name | manager_id |
|----|------|------------|
| 1 | Engineering | 3 |
| 2 | Marketing | 5 |
| 3 | HR | 4 |

**customers table:**
| id | name | email | city |
|----|------|-------|------|
| 1 | Michael Davis | <EMAIL> | New York |
| 2 | Sarah Connor | <EMAIL> | Los Angeles |
| 3 | David Miller | <EMAIL> | Chicago |
| 4 | Lisa Anderson | <EMAIL> | New York |

**orders table:**
| id | customer_id | order_date | total_amount |
|----|-------------|------------|--------------|
| 1 | 1 | 2023-01-15 | 250.00 |
| 2 | 2 | 2023-01-20 | 180.50 |
| 3 | 1 | 2023-02-10 | 320.75 |
| 4 | 3 | 2023-02-15 | 95.25 |
| 5 | 4 | 2023-03-01 | 450.00 |

---

## 1. SELECT Queries

### Basic SELECT

**Query:**

```sql
SELECT name, salary FROM employees;
```

**Expected Output:**
| name | salary |
|------|--------|
| John Smith | 75000.00 |
| Jane Doe | 65000.00 |
| Bob Johnson | 80000.00 |
| Alice Brown | 55000.00 |
| Charlie Wilson | 70000.00 |

**Explanation:** Retrieves specific columns (name and salary) from all rows in the employees table.

### SELECT with WHERE clause

**Query:**

```sql
SELECT * FROM employees WHERE department = 'Engineering';
```

**Expected Output:**
| id | name | department | salary | hire_date |
|----|------|------------|--------|-----------|
| 1 | John Smith | Engineering | 75000.00 | 2020-01-15 |
| 3 | Bob Johnson | Engineering | 80000.00 | 2021-06-10 |

**Explanation:** Filters rows to show only employees in the Engineering department.

### SELECT with multiple conditions (AND/OR)

**Query:**

```sql
SELECT name, salary FROM employees
WHERE department = 'Marketing' OR salary > 75000;
```

**Expected Output:**
| name | salary |
|------|--------|
| Jane Doe | 65000.00 |
| Bob Johnson | 80000.00 |
| Charlie Wilson | 70000.00 |

**Explanation:** Shows employees who are either in Marketing OR have a salary greater than 75000.

---

## 2. JOIN Operations

### INNER JOIN

**Query:**

```sql
SELECT e.name, d.name as department_name
FROM employees e
INNER JOIN departments d ON e.department = d.name;
```

**Expected Output:**
| name | department_name |
|------|-----------------|
| John Smith | Engineering |
| Jane Doe | Marketing |
| Bob Johnson | Engineering |
| Alice Brown | HR |
| Charlie Wilson | Marketing |

**Explanation:** Combines employee data with department data, showing only employees who have matching departments.

### LEFT JOIN

**Query:**

```sql
SELECT c.name, o.order_date, o.total_amount
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id;
```

**Expected Output:**
| name | order_date | total_amount |
|------|------------|--------------|
| Michael Davis | 2023-01-15 | 250.00 |
| Michael Davis | 2023-02-10 | 320.75 |
| Sarah Connor | 2023-01-20 | 180.50 |
| David Miller | 2023-02-15 | 95.25 |
| Lisa Anderson | 2023-03-01 | 450.00 |

**Explanation:** Shows all customers and their orders. Customers without orders would show NULL values for order columns.

---

## 3. Aggregate Functions

### COUNT

**Query:**

```sql
SELECT department, COUNT(*) as employee_count
FROM employees
GROUP BY department;
```

**Expected Output:**
| department | employee_count |
|------------|----------------|
| Engineering | 2 |
| HR | 1 |
| Marketing | 2 |

**Explanation:** Counts the number of employees in each department.

### SUM and AVG

**Query:**

```sql
SELECT department,
       SUM(salary) as total_salary,
       AVG(salary) as average_salary
FROM employees
GROUP BY department;
```

**Expected Output:**
| department | total_salary | average_salary |
|------------|--------------|----------------|
| Engineering | 155000.00 | 77500.00 |
| HR | 55000.00 | 55000.00 |
| Marketing | 135000.00 | 67500.00 |

**Explanation:** Calculates total and average salary for each department.

### HAVING clause

**Query:**

```sql
SELECT department, AVG(salary) as avg_salary
FROM employees
GROUP BY department
HAVING AVG(salary) > 60000;
```

**Expected Output:**
| department | avg_salary |
|------------|------------|
| Engineering | 77500.00 |
| Marketing | 67500.00 |

**Explanation:** Shows departments where the average salary is greater than 60000.

---

## 4. Data Manipulation

### INSERT

**Query:**

```sql
INSERT INTO employees (id, name, department, salary, hire_date)
VALUES (6, 'Tom Anderson', 'Engineering', 72000.00, '2023-04-01');
```

**Expected Result:** One row inserted into the employees table.

### UPDATE

**Query:**

```sql
UPDATE employees
SET salary = salary * 1.10
WHERE department = 'HR';
```

**Expected Result:** Alice Brown's salary updated to 60500.00 (10% increase).

### DELETE

**Query:**

```sql
DELETE FROM employees
WHERE hire_date < '2019-01-01';
```

**Expected Result:** Alice Brown's record deleted (hired before 2019).

---

## 5. Sorting and Filtering

### ORDER BY

**Query:**

```sql
SELECT name, salary FROM employees
ORDER BY salary DESC;
```

**Expected Output:**
| name | salary |
|------|--------|
| Bob Johnson | 80000.00 |
| John Smith | 75000.00 |
| Charlie Wilson | 70000.00 |
| Jane Doe | 65000.00 |
| Alice Brown | 55000.00 |

**Explanation:** Orders employees by salary from highest to lowest.

### LIMIT/TOP

**Query:**

```sql
SELECT name, salary FROM employees
ORDER BY salary DESC
LIMIT 3;
```

**Expected Output:**
| name | salary |
|------|--------|
| Bob Johnson | 80000.00 |
| John Smith | 75000.00 |
| Charlie Wilson | 70000.00 |

**Explanation:** Shows the top 3 highest-paid employees.

### DISTINCT

**Query:**

```sql
SELECT DISTINCT department FROM employees;
```

**Expected Output:**
| department |
|------------|
| Engineering |
| HR |
| Marketing |

**Explanation:** Returns unique department names, eliminating duplicates.

---

## 6. Subqueries

### Subquery in WHERE clause

**Query:**

```sql
SELECT name, salary FROM employees
WHERE salary > (SELECT AVG(salary) FROM employees);
```

**Expected Output:**
| name | salary |
|------|--------|
| John Smith | 75000.00 |
| Bob Johnson | 80000.00 |

**Explanation:** Shows employees whose salary is above the company average (69000.00).

### Subquery in FROM clause

**Query:**

```sql
SELECT dept_stats.department, dept_stats.avg_salary
FROM (
    SELECT department, AVG(salary) as avg_salary
    FROM employees
    GROUP BY department
) as dept_stats
WHERE dept_stats.avg_salary > 65000;
```

**Expected Output:**
| department | avg_salary |
|------------|------------|
| Engineering | 77500.00 |
| Marketing | 67500.00 |

**Explanation:** Uses a subquery to calculate department averages, then filters for departments with average salary > 65000.

### EXISTS subquery

**Query:**

```sql
SELECT c.name FROM customers c
WHERE EXISTS (
    SELECT 1 FROM orders o
    WHERE o.customer_id = c.id AND o.total_amount > 300
);
```

**Expected Output:**
| name |
|------|
| Michael Davis |
| Lisa Anderson |

**Explanation:** Shows customers who have placed orders with a total amount greater than 300.

---

## 7. String Operations

### LIKE pattern matching

**Query:**

```sql
SELECT name FROM employees
WHERE name LIKE 'J%';
```

**Expected Output:**
| name |
|------|
| John Smith |
| Jane Doe |

**Explanation:** Finds employees whose names start with 'J'. The % wildcard matches any sequence of characters.

### LIKE with multiple patterns

**Query:**

```sql
SELECT name FROM employees
WHERE name LIKE '%son' OR name LIKE '%Brown';
```

**Expected Output:**
| name |
|------|
| Bob Johnson |
| Alice Brown |

**Explanation:** Finds employees whose names end with 'son' or 'Brown'.

### CONCAT function

**Query:**

```sql
SELECT CONCAT(name, ' - ', department) as employee_info
FROM employees;
```

**Expected Output:**
| employee_info |
|---------------|
| John Smith - Engineering |
| Jane Doe - Marketing |
| Bob Johnson - Engineering |
| Alice Brown - HR |
| Charlie Wilson - Marketing |

**Explanation:** Concatenates employee name with their department, separated by ' - '.

### UPPER and LOWER functions

**Query:**

```sql
SELECT UPPER(name) as name_upper,
       LOWER(department) as dept_lower
FROM employees
WHERE id <= 3;
```

**Expected Output:**
| name_upper | dept_lower |
|------------|------------|
| JOHN SMITH | engineering |
| JANE DOE | marketing |
| BOB JOHNSON | engineering |

**Explanation:** Converts names to uppercase and departments to lowercase for the first 3 employees.

### String length and substring

**Query:**

```sql
SELECT name,
       LENGTH(name) as name_length,
       SUBSTRING(name, 1, 5) as name_short
FROM employees;
```

**Expected Output:**
| name | name_length | name_short |
|------|-------------|------------|
| John Smith | 10 | John |
| Jane Doe | 8 | Jane |
| Bob Johnson | 11 | Bob J |
| Alice Brown | 11 | Alice |
| Charlie Wilson | 14 | Charl |

**Explanation:** Shows the length of each name and the first 5 characters of each name.

---

## 8. Date Functions

### Date filtering

**Query:**

```sql
SELECT name, hire_date FROM employees
WHERE hire_date >= '2020-01-01';
```

**Expected Output:**
| name | hire_date |
|------|-----------|
| John Smith | 2020-01-15 |
| Bob Johnson | 2021-06-10 |
| Charlie Wilson | 2020-09-12 |

**Explanation:** Shows employees hired on or after January 1, 2020.

### Date functions - YEAR, MONTH, DAY

**Query:**

```sql
SELECT name,
       hire_date,
       YEAR(hire_date) as hire_year,
       MONTH(hire_date) as hire_month
FROM employees;
```

**Expected Output:**
| name | hire_date | hire_year | hire_month |
|------|-----------|-----------|------------|
| John Smith | 2020-01-15 | 2020 | 1 |
| Jane Doe | 2019-03-20 | 2019 | 3 |
| Bob Johnson | 2021-06-10 | 2021 | 6 |
| Alice Brown | 2018-11-05 | 2018 | 11 |
| Charlie Wilson | 2020-09-12 | 2020 | 9 |

**Explanation:** Extracts year and month components from the hire_date.

### Date arithmetic

**Query:**

```sql
SELECT name,
       hire_date,
       DATEDIFF(CURDATE(), hire_date) as days_employed
FROM employees;
```

**Expected Output (assuming current date is 2023-10-08):**
| name | hire_date | days_employed |
|------|-----------|---------------|
| John Smith | 2020-01-15 | 1362 |
| Jane Doe | 2019-03-20 | 1663 |
| Bob Johnson | 2021-06-10 | 851 |
| Alice Brown | 2018-11-05 | 1798 |
| Charlie Wilson | 2020-09-12 | 1122 |

**Explanation:** Calculates the number of days each employee has been with the company.

### Date formatting

**Query:**

```sql
SELECT name,
       DATE_FORMAT(hire_date, '%M %d, %Y') as formatted_date
FROM employees;
```

**Expected Output:**
| name | formatted_date |
|------|----------------|
| John Smith | January 15, 2020 |
| Jane Doe | March 20, 2019 |
| Bob Johnson | June 10, 2021 |
| Alice Brown | November 05, 2018 |
| Charlie Wilson | September 12, 2020 |

**Explanation:** Formats hire dates in a more readable format (Month Day, Year).

---

## 9. Advanced Examples

### Complex JOIN with aggregation

**Query:**

```sql
SELECT c.city,
       COUNT(o.id) as total_orders,
       SUM(o.total_amount) as total_revenue
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.city
ORDER BY total_revenue DESC;
```

**Expected Output:**
| city | total_orders | total_revenue |
|------|--------------|---------------|
| New York | 3 | 1020.75 |
| Los Angeles | 1 | 180.50 |
| Chicago | 1 | 95.25 |

**Explanation:** Shows order statistics by city, including cities with no orders.

### Window functions (if supported)

**Query:**

```sql
SELECT name,
       department,
       salary,
       RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dept_rank
FROM employees;
```

**Expected Output:**
| name | department | salary | dept_rank |
|------|------------|--------|-----------|
| Bob Johnson | Engineering | 80000.00 | 1 |
| John Smith | Engineering | 75000.00 | 2 |
| Alice Brown | HR | 55000.00 | 1 |
| Charlie Wilson | Marketing | 70000.00 | 1 |
| Jane Doe | Marketing | 65000.00 | 2 |

**Explanation:** Ranks employees by salary within each department.

### CASE statement

**Query:**

```sql
SELECT name,
       salary,
       CASE
           WHEN salary >= 75000 THEN 'High'
           WHEN salary >= 60000 THEN 'Medium'
           ELSE 'Low'
       END as salary_category
FROM employees;
```

**Expected Output:**
| name | salary | salary_category |
|------|--------|-----------------|
| John Smith | 75000.00 | High |
| Jane Doe | 65000.00 | Medium |
| Bob Johnson | 80000.00 | High |
| Alice Brown | 55000.00 | Low |
| Charlie Wilson | 70000.00 | Medium |

**Explanation:** Categorizes employees based on their salary ranges using conditional logic.

---

## Notes

- SQL syntax may vary slightly between different database systems (MySQL, PostgreSQL, SQL Server, Oracle, etc.)
- Some functions like `DATEDIFF`, `DATE_FORMAT`, and window functions may have different syntax in different databases
- Always check your specific database documentation for exact syntax and available functions
- Use appropriate data types and constraints when creating tables in production environments
